# Cloudflare Stream Livestream Demo

Một ứng dụng React đơn giản để xem livestream sử dụng Cloudflare Stream API.

## 🚀 Tính năng

- **📺 Xem Livestream**: Hỗ trợ xem livestream từ Cloudflare Stream
- **🎮 Hai loại Player**: Stream Player (Iframe) và Custom HLS Player
- **📊 Kiểm tra trạng thái**: Real-time status của livestream
- **📱 Responsive**: Giao diện thích ứng mọi thiết bị
- **🎨 UI/UX hiện đại**: Gradient, animations và hiệu ứng mượt mà

## 🛠️ Công nghệ sử dụng

- **React 18**: Thư viện JavaScript hiện đại
- **Cloudflare Stream API**: Để streaming và kiểm tra trạng thái
- **HLS.js**: Thư viện để phát HLS trên các trình duyệt
- **Babel**: Compile JSX trực tiếp trong trình duyệt
- **CSS3**: Modern styling với flexbox và animations

## 📦 Cài đặt và Chạy

### Cách 1: Chạy trực tiếp (Khuyến nghị)
```bash
# Clone repository
git clone https://github.com/yourusername/cloudflare-view-livestream-demo.git
cd cloudflare-view-livestream-demo

# Mở file index.html trong trình duyệt
# Hoặc sử dụng Live Server extension trong VS Code
```

### Cách 2: Sử dụng npm (Tùy chọn)
```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev
```

## 🎯 Cách sử dụng

### 1. Lấy thông tin từ Cloudflare Dashboard
- Đăng nhập vào [Cloudflare Dashboard](https://dash.cloudflare.com)
- Vào **Stream** > **Live Inputs**
- Lấy **Customer Code** từ URL (ví dụ: `customer-f33zs165nr7gyfy4`)
- Lấy **Live Input ID** hoặc **Video ID**

### 2. Nhập thông tin vào ứng dụng
- **Customer Code**: Phần sau "customer-" trong URL
- **Stream ID**: Live Input ID (để xem stream hiện tại) hoặc Video ID (để xem video cụ thể)

### 3. Chọn loại player
- **Stream Player (Iframe)**: Player chính thức của Cloudflare (khuyến nghị)
- **Custom HLS Player**: Sử dụng HTML5 video với HLS.js

### 4. Kiểm tra trạng thái
Nhấn nút "🔍 Kiểm tra trạng thái" để xem stream có đang live không

## 📋 API Endpoints được sử dụng

### Stream Player URL
```
https://customer-{CODE}.cloudflarestream.com/{ID}/iframe
```

### HLS Manifest URL
```
https://customer-{CODE}.cloudflarestream.com/{ID}/manifest/video.m3u8
```

### Lifecycle API (Kiểm tra trạng thái)
```
https://customer-{CODE}.cloudflarestream.com/{ID}/lifecycle
```

## 🌟 Screenshots

### Giao diện chính
![Main Interface](screenshots/main-interface.png)

### Livestream Viewer
![Livestream Viewer](screenshots/livestream-viewer.png)

## 🔧 Cấu trúc dự án

```
cloudflare-view-livestream-demo/
├── index.html              # File chính chứa toàn bộ ứng dụng
├── package.json            # Cấu hình npm (tùy chọn)
├── vite.config.js          # Cấu hình Vite (tùy chọn)
├── src/                    # Source files (cho npm build)
│   ├── main.jsx
│   ├── App.jsx
│   ├── App.css
│   └── index.css
└── README.md               # Documentation
```

## 🤝 Đóng góp

1. Fork dự án
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📝 License

Dự án này được phân phối dưới MIT License. Xem file `LICENSE` để biết thêm thông tin.

## 🙏 Acknowledgments

- [Cloudflare Stream](https://developers.cloudflare.com/stream/) - Streaming platform
- [React](https://reactjs.org/) - JavaScript library
- [HLS.js](https://github.com/video-dev/hls.js/) - HLS player library
- [Babel](https://babeljs.io/) - JavaScript compiler

## 📞 Liên hệ

Nếu bạn có câu hỏi hoặc góp ý, hãy tạo issue trên GitHub repository này.

---

⭐ Nếu dự án này hữu ích, hãy cho một star nhé!
