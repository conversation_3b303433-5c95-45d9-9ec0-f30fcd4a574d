<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My React App</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .app {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .counter {
            margin: 30px 0;
        }

        .count-display {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(0);
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .feature {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .feature p {
            margin: 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        function App() {
            const [count, setCount] = useState(0);
            const [message, setMessage] = useState('Chào mừng bạn!');

            useEffect(() => {
                if (count === 0) {
                    setMessage('Hãy bắt đầu đếm!');
                } else if (count < 5) {
                    setMessage('Tuyệt vời! Tiếp tục nhé!');
                } else if (count < 10) {
                    setMessage('Bạn đang làm rất tốt!');
                } else {
                    setMessage('Wow! Bạn thật kiên trì!');
                }
            }, [count]);

            const increment = () => setCount(count + 1);
            const decrement = () => setCount(Math.max(0, count - 1));
            const reset = () => setCount(0);

            return (
                <div className="app">
                    <h1>🚀 My React App</h1>
                    <p>{message}</p>

                    <div className="counter">
                        <div className="count-display">{count}</div>
                        <button onClick={increment}>➕ Tăng</button>
                        <button onClick={decrement}>➖ Giảm</button>
                        <button onClick={reset}>🔄 Reset</button>
                    </div>

                    <div className="features">
                        <div className="feature">
                            <h3>⚡ React Hooks</h3>
                            <p>Sử dụng useState và useEffect để quản lý state và side effects</p>
                        </div>
                        <div className="feature">
                            <h3>🎨 CSS Modern</h3>
                            <p>Gradient backgrounds, box shadows và animations mượt mà</p>
                        </div>
                        <div className="feature">
                            <h3>📱 Responsive</h3>
                            <p>Giao diện thích ứng với mọi kích thước màn hình</p>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
