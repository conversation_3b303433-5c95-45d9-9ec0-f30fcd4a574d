<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My React App</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .app {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            max-width: 500px;
            width: 90%;
        }

        h1 {
            color: #333;
            margin-bottom: 30px;
            font-size: 2.5em;
        }

        .counter {
            margin: 30px 0;
        }

        .count-display {
            font-size: 3em;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }

        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px;
            border-radius: 50px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        button:active {
            transform: translateY(0);
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .feature {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .feature h3 {
            margin: 0 0 10px 0;
            color: #333;
        }

        .feature p {
            margin: 0;
            color: #666;
        }

        /* Tab Navigation */
        .tab-navigation {
            display: flex;
            gap: 10px;
            margin-bottom: 30px;
            justify-content: center;
        }

        .tab-button {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e9ecef;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            background: #e9ecef;
            transform: translateY(-1px);
        }

        .tab-button.active {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
        }

        /* Livestream Viewer Styles */
        .livestream-viewer {
            max-width: 800px;
            margin: 0 auto;
        }

        .stream-controls {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
        }

        .input-group {
            margin-bottom: 20px;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .stream-input, .stream-select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .stream-input:focus, .stream-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .check-button {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .check-button:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .check-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Stream Status */
        .stream-status {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            font-weight: 600;
        }

        .stream-status.live {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .stream-status.offline {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .stream-status.live .status-indicator {
            background: #28a745;
        }

        .stream-status.offline .status-indicator {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        /* Video Container */
        .video-container {
            margin-bottom: 30px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .stream-iframe {
            width: 100%;
            height: 450px;
            border: none;
        }

        .stream-video {
            width: 100%;
            height: 450px;
            background: #000;
        }

        /* Stream Info */
        .stream-info {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .stream-info h3 {
            margin-top: 0;
            color: #333;
        }

        .info-item {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .info-item:last-child {
            border-bottom: none;
        }

        .info-item strong {
            color: #667eea;
            margin-right: 10px;
        }

        .info-item a {
            color: #667eea;
            text-decoration: none;
            word-break: break-all;
        }

        .info-item a:hover {
            text-decoration: underline;
        }

        .info-item code {
            background: #e9ecef;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            word-break: break-all;
        }

        /* Counter Demo Styles */
        .counter-demo {
            max-width: 600px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect, useRef } = React;

        // Component để xem Cloudflare Stream Livestream
        function LiveStreamViewer() {
            const [streamId, setStreamId] = useState('');
            const [customerCode, setCustomerCode] = useState('');
            const [isLive, setIsLive] = useState(false);
            const [streamStatus, setStreamStatus] = useState('');
            const [playerType, setPlayerType] = useState('iframe'); // 'iframe' hoặc 'hls'
            const [loading, setLoading] = useState(false);
            const videoRef = useRef(null);

            // Kiểm tra trạng thái livestream
            const checkStreamStatus = async () => {
                if (!streamId || !customerCode) return;

                setLoading(true);
                try {
                    const response = await fetch(`https://customer-${customerCode}.cloudflarestream.com/${streamId}/lifecycle`);
                    const data = await response.json();

                    setIsLive(data.live);
                    setStreamStatus(data.live ? 'Đang phát trực tiếp' : 'Không có stream');
                } catch (error) {
                    console.error('Lỗi khi kiểm tra trạng thái stream:', error);
                    setStreamStatus('Không thể kiểm tra trạng thái');
                } finally {
                    setLoading(false);
                }
            };

            // Load HLS player nếu cần
            const loadHLSPlayer = () => {
                if (playerType === 'hls' && streamId && customerCode && videoRef.current) {
                    const video = videoRef.current;
                    const hlsUrl = `https://customer-${customerCode}.cloudflarestream.com/${streamId}/manifest/video.m3u8`;

                    if (video.canPlayType('application/vnd.apple.mpegurl')) {
                        // Safari hỗ trợ HLS native
                        video.src = hlsUrl;
                    } else {
                        // Các trình duyệt khác cần HLS.js
                        if (window.Hls && window.Hls.isSupported()) {
                            const hls = new window.Hls();
                            hls.loadSource(hlsUrl);
                            hls.attachMedia(video);
                        } else {
                            setStreamStatus('Trình duyệt không hỗ trợ HLS');
                        }
                    }
                }
            };

            useEffect(() => {
                loadHLSPlayer();
            }, [playerType, streamId, customerCode]);

            const handleStreamIdChange = (e) => {
                setStreamId(e.target.value);
            };

            const handleCustomerCodeChange = (e) => {
                setCustomerCode(e.target.value);
            };

            const getIframeUrl = () => {
                if (!streamId || !customerCode) return '';
                return `https://customer-${customerCode}.cloudflarestream.com/${streamId}/iframe`;
            };

            return (
                <div className="livestream-viewer">
                    <h2>📺 Cloudflare Stream Viewer</h2>

                    <div className="stream-controls">
                        <div className="input-group">
                            <label>Customer Code:</label>
                            <input
                                type="text"
                                value={customerCode}
                                onChange={handleCustomerCodeChange}
                                placeholder="Nhập customer code (ví dụ: f33zs165nr7gyfy4)"
                                className="stream-input"
                            />
                        </div>

                        <div className="input-group">
                            <label>Stream ID (Live Input ID hoặc Video ID):</label>
                            <input
                                type="text"
                                value={streamId}
                                onChange={handleStreamIdChange}
                                placeholder="Nhập Live Input ID hoặc Video ID"
                                className="stream-input"
                            />
                        </div>

                        <div className="input-group">
                            <label>Loại Player:</label>
                            <select
                                value={playerType}
                                onChange={(e) => setPlayerType(e.target.value)}
                                className="stream-select"
                            >
                                <option value="iframe">Stream Player (Iframe)</option>
                                <option value="hls">Custom HLS Player</option>
                            </select>
                        </div>

                        <button onClick={checkStreamStatus} disabled={loading} className="check-button">
                            {loading ? '⏳ Đang kiểm tra...' : '🔍 Kiểm tra trạng thái'}
                        </button>
                    </div>

                    {streamStatus && (
                        <div className={`stream-status ${isLive ? 'live' : 'offline'}`}>
                            <span className="status-indicator"></span>
                            {streamStatus}
                        </div>
                    )}

                    {streamId && customerCode && (
                        <div className="video-container">
                            {playerType === 'iframe' ? (
                                <iframe
                                    src={getIframeUrl()}
                                    className="stream-iframe"
                                    allow="accelerometer; gyroscope; autoplay; encrypted-media; picture-in-picture;"
                                    allowFullScreen
                                    title="Cloudflare Stream Player"
                                />
                            ) : (
                                <video
                                    ref={videoRef}
                                    controls
                                    autoPlay
                                    muted
                                    className="stream-video"
                                >
                                    Trình duyệt của bạn không hỗ trợ video HTML5.
                                </video>
                            )}
                        </div>
                    )}

                    <div className="stream-info">
                        <h3>📋 Thông tin Stream</h3>
                        <div className="info-item">
                            <strong>Customer Code:</strong> {customerCode || 'Chưa nhập'}
                        </div>
                        <div className="info-item">
                            <strong>Stream ID:</strong> {streamId || 'Chưa nhập'}
                        </div>
                        {streamId && customerCode && (
                            <>
                                <div className="info-item">
                                    <strong>Stream Player URL:</strong>
                                    <a href={getIframeUrl()} target="_blank" rel="noopener noreferrer">
                                        {getIframeUrl()}
                                    </a>
                                </div>
                                <div className="info-item">
                                    <strong>HLS Manifest URL:</strong>
                                    <code>https://customer-{customerCode}.cloudflarestream.com/{streamId}/manifest/video.m3u8</code>
                                </div>
                            </>
                        )}
                    </div>
                </div>
            );
        }

        // Component chính của ứng dụng
        function App() {
            const [currentTab, setCurrentTab] = useState('livestream');

            return (
                <div className="app">
                    <h1>🚀 Cloudflare Stream Demo</h1>

                    <div className="tab-navigation">
                        <button
                            className={`tab-button ${currentTab === 'livestream' ? 'active' : ''}`}
                            onClick={() => setCurrentTab('livestream')}
                        >
                            📺 Livestream
                        </button>
                        <button
                            className={`tab-button ${currentTab === 'demo' ? 'active' : ''}`}
                            onClick={() => setCurrentTab('demo')}
                        >
                            🎮 Demo Counter
                        </button>
                    </div>

                    {currentTab === 'livestream' ? (
                        <LiveStreamViewer />
                    ) : (
                        <CounterDemo />
                    )}
                </div>
            );
        }

        // Component demo counter cũ
        function CounterDemo() {
            const [count, setCount] = useState(0);
            const [message, setMessage] = useState('Chào mừng bạn!');

            useEffect(() => {
                if (count === 0) {
                    setMessage('Hãy bắt đầu đếm!');
                } else if (count < 5) {
                    setMessage('Tuyệt vời! Tiếp tục nhé!');
                } else if (count < 10) {
                    setMessage('Bạn đang làm rất tốt!');
                } else {
                    setMessage('Wow! Bạn thật kiên trì!');
                }
            }, [count]);

            const increment = () => setCount(count + 1);
            const decrement = () => setCount(Math.max(0, count - 1));
            const reset = () => setCount(0);

            return (
                <div className="counter-demo">
                    <h2>🎮 Demo Counter</h2>
                    <p>{message}</p>

                    <div className="counter">
                        <div className="count-display">{count}</div>
                        <button onClick={increment}>➕ Tăng</button>
                        <button onClick={decrement}>➖ Giảm</button>
                        <button onClick={reset}>🔄 Reset</button>
                    </div>

                    <div className="features">
                        <div className="feature">
                            <h3>⚡ React Hooks</h3>
                            <p>Sử dụng useState và useEffect để quản lý state và side effects</p>
                        </div>
                        <div className="feature">
                            <h3>🎨 CSS Modern</h3>
                            <p>Gradient backgrounds, box shadows và animations mượt mà</p>
                        </div>
                        <div className="feature">
                            <h3>📱 Responsive</h3>
                            <p>Giao diện thích ứng với mọi kích thước màn hình</p>
                        </div>
                    </div>
                </div>
            );
        }

        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
